<template>
  <div class="standard-selector">
    <!-- 搜索区域 -->
    <div class="search-section" v-if="showSearch">
      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="50px"
        class="search-form"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入名称"
            clearable
            @keyup.enter="handleQuery"
            size="small"
          />
        </el-form-item>
        <el-form-item label="年份" prop="publishYear">
          <el-date-picker
            v-model="queryParams.publishYear"
            type="year"
            value-format="YYYY"
            placeholder="年份"
            clearable
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery" size="small" type="primary">
            <Icon icon="ep:search" />
          </el-button>
          <el-button @click="resetQuery" size="small">
            <Icon icon="ep:refresh" />
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表区域 -->
    <div class="list-section">
      <el-table
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        :highlight-current-row="true"
        @current-change="handleCurrentChange"
        :height="tableHeight"
        class="selector-table"
      >
        <el-table-column
          label="名称"
          prop="name"
          min-width="120"
          show-overflow-tooltip
        />

        <el-table-column
          label="分类"
          prop="category"
          width="100"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag v-if="scope.row.category" size="small" type="success">
              {{ scope.row.category }}
            </el-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>

        <el-table-column
          label="年份"
          prop="publishYear"
          width="70"
          align="center"
        >
          <template #default="scope">
            <el-tag size="small" type="info">
              {{ scope.row.publishYear }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column 
          label="状态" 
          prop="enabled" 
          width="60"
          align="center"
          v-if="showStatus"
        >
          <template #default="scope">
            <el-tag 
              :type="scope.row.enabled === 1 ? 'success' : 'danger'" 
              size="small"
            >
              {{ scope.row.enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section" v-if="showPagination">
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          layout="prev, pager, next, sizes"
          :page-sizes="[10, 20, 50]"
          small
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'

// 定义组件名称
defineOptions({ name: 'StandardSelector' })

// 组件属性
interface StandardSelectorProps {
  /** 是否显示搜索区域 */
  showSearch?: boolean
  /** 是否显示分页 */
  showPagination?: boolean
  /** 是否显示状态列 */
  showStatus?: boolean
  /** 表格高度 */
  tableHeight?: string | number
  /** 每页显示数量 */
  pageSize?: number
  /** 是否只显示启用的标准 */
  enabledOnly?: boolean
}

const props = withDefaults(defineProps<StandardSelectorProps>(), {
  showSearch: true,
  showPagination: true,
  showStatus: false,
  tableHeight: 400,
  pageSize: 10,
  enabledOnly: false
})

// 组件事件
const emit = defineEmits<{
  'select': [standard: CitystandardVO]
  'change': [standard: CitystandardVO | null]
}>()

// 响应式数据
const loading = ref(true)
const list = ref<CitystandardVO[]>([])
const total = ref(0)
const selectedStandard = ref<CitystandardVO | null>(null)

const queryParams = reactive({
  pageNo: 1,
  pageSize: props.pageSize,
  name: undefined,
  publishYear: undefined,
  enabled: props.enabledOnly ? 1 : undefined
})

const queryFormRef = ref()

// 方法
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CitystandardApi.getCitystandardPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 表格行选择变化 */
const handleCurrentChange = (currentRow: CitystandardVO | null) => {
  selectedStandard.value = currentRow
  if (currentRow) {
    console.log('📋 选择标准:', currentRow.name, 'ID:', currentRow.id)
    emit('select', currentRow)
  }
  emit('change', currentRow)
}

/** 获取当前选中的标准 */
const getSelectedStandard = (): CitystandardVO | null => {
  return selectedStandard.value
}

/** 清除选择 */
const clearSelection = () => {
  selectedStandard.value = null
  emit('change', null)
}

/** 刷新列表 */
const refresh = () => {
  getList()
}

/** 根据ID选中标准 */
const selectById = (id: number) => {
  const standard = list.value.find(item => item.id === id)
  if (standard) {
    selectedStandard.value = standard
    emit('select', standard)
    emit('change', standard)
  }
}

// 暴露方法
defineExpose({
  getSelectedStandard,
  clearSelection,
  refresh,
  selectById,
  getList
})

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.standard-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-section {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 12px;

    .search-form {
      margin: 0;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }

      :deep(.el-input) {
        width: 120px;
      }

      :deep(.el-date-editor) {
        width: 100px;
      }

      :deep(.el-button) {
        padding: 5px 8px;
        margin-left: 4px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
  
  .list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .selector-table {
      flex: 1;
      
      :deep(.el-table__row) {
        cursor: pointer;
        transition: background-color 0.3s ease;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &.current-row {
          background-color: #ecf5ff;
        }
      }
      
      .name-cell {
        .name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 2px;
        }
        
        .category {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .pagination-section {
      padding: 16px 0;
      display: flex;
      justify-content: center;
    }
  }
}

// 紧凑模式
.standard-selector.compact {
  .search-section {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .pagination-section {
    padding: 12px 0;
  }
}
</style>
