<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px" class="wizard-dialog">
    <!-- 步骤指示器 -->
    <div class="wizard-header">
      <div class="steps-container">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="step-item"
          :class="{
            'step-active': currentStep === index,
            'step-completed': currentStep > index
          }"
          @click="handleStepClick(index)"
        >
          <div class="step-number">
            <Icon v-if="currentStep > index" icon="ep:check" />
            <span v-else>{{ index + 1 }}</span>
          </div>
          <span class="step-title">{{ step.title }}</span>
        </div>
      </div>
    </div>

    <!-- 表单内容区域 -->
    <div class="wizard-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
      >
        <!-- 步骤1: 基本信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="序号" prop="seqNo">
                  <el-input v-model="formData.seqNo" placeholder="请输入序号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="指标项" prop="itemName">
                  <el-input v-model="formData.itemName" placeholder="请输入指标项" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="指标项别名" prop="itemAlias">
                  <el-input v-model="formData.itemAlias" placeholder="请输入指标项别名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="指标编号" prop="itemCode">
                  <el-input v-model="formData.itemCode" placeholder="请输入指标编号" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 步骤2: 分类配置 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="form-section">
            <h4 class="section-title">分类配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否核心指标" prop="isCore">
                  <el-radio-group v-model="formData.isCore">
                    <el-radio
                      v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                      :key="dict.value"
                      :label="dict.value"
                    >
                      {{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否国家基础指标" prop="isNational">
                  <el-radio-group v-model="formData.isNational">
                    <el-radio
                      v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                      :key="dict.value"
                      :label="dict.value"
                    >
                      {{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否地方特色指标" prop="isLocal">
                  <el-radio-group v-model="formData.isLocal">
                    <el-radio
                      v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                      :key="dict.value"
                      :label="dict.value"
                    >
                      {{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否小程序采集" prop="isApplet">
                  <el-radio-group v-model="formData.isApplet">
                    <el-radio
                      v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                      :key="dict.value"
                      :label="dict.value"
                    >
                      {{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 步骤3: 维度设置 -->
        <div v-show="currentStep === 2" class="step-content">
          <!-- 维度信息 -->
          <div class="form-section">
            <h4 class="section-title">维度信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="一级维度" prop="dimensionLevel1">
                  <el-input v-model="formData.dimensionLevel1" placeholder="请输入一级维度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二级维度" prop="dimensionLevel2">
                  <el-input v-model="formData.dimensionLevel2" placeholder="请输入二级维度" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 标准规格 -->
          <div class="form-section">
            <h4 class="section-title">标准规格</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="解释" prop="explanation">
                  <el-input v-model="formData.explanation" placeholder="请输入解释" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评价标准" prop="evaluationCriteria">
                  <el-input v-model="formData.evaluationCriteria" placeholder="请输入评价标准" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据分解" prop="dataDecomposition">
                  <el-input v-model="formData.dataDecomposition" placeholder="请输入数据分解" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据格式" prop="dataFormat">
                  <el-input v-model="formData.dataFormat" placeholder="请输入数据格式" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 步骤4: 业务管理 -->
        <div v-show="currentStep === 3" class="step-content">
          <!-- 业务信息 -->
          <div class="form-section">
            <h4 class="section-title">业务信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据来源" prop="dataSource">
                  <el-input v-model="formData.dataSource" placeholder="请输入数据来源" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="责任部门" prop="responsibleDept">
                  <el-input v-model="formData.responsibleDept" placeholder="请输入责任部门" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 详细描述 -->
          <div class="form-section">
            <h4 class="section-title">详细描述</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="采集内容" prop="collectionContent">
                  <Editor v-model="formData.collectionContent" height="120px" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="案例示意" prop="caseDemo">
                  <Editor v-model="formData.caseDemo" height="120px" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 导航按钮区域 -->
    <div class="wizard-footer">
      <div class="footer-left">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
      <div class="footer-right">
        <el-button @click="handlePrevStep" :disabled="currentStep === 0">
          上一步
        </el-button>
        <el-button
          v-if="currentStep < steps.length - 1"
          type="primary"
          @click="handleNextStep"
        >
          下一步
        </el-button>
        <el-button
          v-else
          type="primary"
          @click="submitForm"
          :loading="formLoading"
        >
          完成
        </el-button>
      </div>
    </div>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'

/** 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 表单 */
defineOptions({ name: 'CitystandardItemForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const currentStep = ref(0) // 当前步骤

// 步骤配置
const steps = [
  { title: '基本信息', key: 'basic' },
  { title: '分类配置', key: 'classification' },
  { title: '维度信息', key: 'dimension' },
  { title: '详细描述', key: 'description' },
  { title: '业务管理', key: 'business' }
]
const formData = ref({
  id: undefined,
  citystandardId: undefined, // 城市标准体系ID
  seqNo: undefined,
  isCore: undefined,
  isNational: undefined,
  isLocal: undefined,
  isApplet: undefined,
  dimensionLevel1: undefined,
  dimensionLevel2: undefined,
  itemName: undefined,
  itemAlias: undefined,
  itemCode: undefined,
  explanation: undefined,
  evaluationCriteria: undefined,
  dataDecomposition: undefined,
  dataFormat: undefined,
  collectionContent: undefined,
  dataSource: undefined,
  responsibleDept: undefined,
  caseDemo: undefined
})
const formRules = reactive({
  seqNo: [{ required: true, message: '序号不能为空', trigger: 'blur' }],
  isCore: [{ required: true, message: '是否核心指标不能为空', trigger: 'blur' }],
  isNational: [{ required: true, message: '是否国家基础指标不能为空', trigger: 'blur' }],
  isLocal: [{ required: true, message: '是否地方特色指标不能为空', trigger: 'blur' }],
  isApplet: [{ required: true, message: '是否小程序采集不能为空', trigger: 'blur' }],
  dimensionLevel1: [{ required: true, message: '一级维度不能为空', trigger: 'blur' }],
  itemName: [{ required: true, message: '指标项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 步骤点击处理 */
const handleStepClick = (index: number) => {
  // 只允许点击已完成的步骤或当前步骤
  if (index <= currentStep.value) {
    currentStep.value = index
  }
}

/** 上一步 */
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/** 下一步 */
const handleNextStep = async () => {
  // 验证当前步骤的表单字段
  const isValid = await validateCurrentStep()
  if (isValid && currentStep.value < steps.length - 1) {
    currentStep.value++
  }
}

/** 验证当前步骤 */
const validateCurrentStep = async (): Promise<boolean> => {
  try {
    // 根据当前步骤验证对应的字段
    const fieldsToValidate = getFieldsByStep(currentStep.value)
    if (fieldsToValidate.length > 0) {
      await formRef.value?.validateField(fieldsToValidate)
    }
    return true
  } catch (error) {
    return false
  }
}

/** 根据步骤获取需要验证的字段 */
const getFieldsByStep = (step: number): string[] => {
  switch (step) {
    case 0: // 基本信息
      return ['seqNo', 'itemName', 'itemAlias', 'itemCode']
    case 1: // 分类配置
      return ['isCore', 'isNational', 'isLocal', 'isApplet']
    case 2: // 维度设置
      return ['dimensionLevel1', 'dimensionLevel2', 'explanation', 'evaluationCriteria', 'dataDecomposition', 'dataFormat']
    case 3: // 业务管理
      return ['dataSource', 'responsibleDept', 'collectionContent', 'caseDemo']
    default:
      return []
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, standardId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  currentStep.value = 0 // 重置到第一步
  resetForm()

  // 设置城市标准体系ID
  if (standardId) {
    formData.value.citystandardId = standardId
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CitystandardItemApi.getCitystandardItem(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CitystandardItemVO
    if (formType.value === 'create') {
      await CitystandardItemApi.createCitystandardItem(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardItemApi.updateCitystandardItem(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    citystandardId: undefined,
    seqNo: undefined,
    isCore: undefined,
    isNational: undefined,
    isLocal: undefined,
    isApplet: undefined,
    dimensionLevel1: undefined,
    dimensionLevel2: undefined,
    itemName: undefined,
    itemAlias: undefined,
    itemCode: undefined,
    explanation: undefined,
    evaluationCriteria: undefined,
    dataDecomposition: undefined,
    dataFormat: undefined,
    collectionContent: undefined,
    dataSource: undefined,
    responsibleDept: undefined,
    caseDemo: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.wizard-dialog {
  :deep(.el-dialog) {
    height: 90vh;
    max-height: 900px;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__footer) {
    display: none; // 隐藏默认的footer
  }
}

.wizard-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e4e7ed;

  .steps-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;

    .step-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin: 0 30px;
      transition: all 0.3s ease;

      &.step-active {
        .step-number {
          background: #409eff;
          color: white;
          border-color: #409eff;
        }
        .step-title {
          color: #409eff;
          font-weight: 600;
        }
      }

      &.step-completed {
        .step-number {
          background: #67c23a;
          color: white;
          border-color: #67c23a;
        }
        .step-title {
          color: #67c23a;
        }
      }

      .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid #dcdfe6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 14px;
        font-weight: 600;
        background: white;
        color: #909399;
        transition: all 0.3s ease;
      }

      .step-title {
        font-size: 14px;
        color: #909399;
        white-space: nowrap;
        transition: all 0.3s ease;
      }
    }
  }
}

.wizard-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;

  .step-content {
    min-height: 400px; // 最小高度确保一致性
    overflow: visible;
  }
}

.wizard-footer {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-left {
    // 左侧取消按钮
  }

  .footer-right {
    display: flex;
    gap: 12px;
  }
}

.form-section {
  margin-bottom: 24px;

  .section-title {
    margin: 0 0 16px 0;
    padding: 8px 12px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    border-radius: 4px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}

// 富文本编辑器样式优化
:deep(.editor-container) {
  border-radius: 4px;

  .ql-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .ql-container {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .form-section {
    .el-row .el-col {
      margin-bottom: 12px;
    }
  }
}
</style>