<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <!-- 基础信息组 -->
      <div class="form-section">
        <h4 class="section-title">基础信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="序号" prop="seqNo">
              <el-input v-model="formData.seqNo" placeholder="请输入序号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标项" prop="itemName">
              <el-input v-model="formData.itemName" placeholder="请输入指标项" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标项别名" prop="itemAlias">
              <el-input v-model="formData.itemAlias" placeholder="请输入指标项别名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编号" prop="itemCode">
              <el-input v-model="formData.itemCode" placeholder="请输入指标编号" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 分类标识组 -->
      <div class="form-section">
        <h4 class="section-title">分类标识</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否核心指标" prop="isCore">
              <el-radio-group v-model="formData.isCore">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否国家基础指标" prop="isNational">
              <el-radio-group v-model="formData.isNational">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否地方特色指标" prop="isLocal">
              <el-radio-group v-model="formData.isLocal">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否小程序采集" prop="isApplet">
              <el-radio-group v-model="formData.isApplet">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 维度信息组 -->
      <div class="form-section">
        <h4 class="section-title">维度信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="一级维度" prop="dimensionLevel1">
              <el-input v-model="formData.dimensionLevel1" placeholder="请输入一级维度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级维度" prop="dimensionLevel2">
              <el-input v-model="formData.dimensionLevel2" placeholder="请输入二级维度" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 标准规格组 -->
      <div class="form-section">
        <h4 class="section-title">标准规格</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="解释" prop="explanation">
              <el-input v-model="formData.explanation" placeholder="请输入解释" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价标准" prop="evaluationCriteria">
              <el-input v-model="formData.evaluationCriteria" placeholder="请输入评价标准" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据分解" prop="dataDecomposition">
              <el-input v-model="formData.dataDecomposition" placeholder="请输入数据分解" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据格式" prop="dataFormat">
              <el-input v-model="formData.dataFormat" placeholder="请输入数据格式" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 业务信息组 -->
      <div class="form-section">
        <h4 class="section-title">业务信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据来源" prop="dataSource">
              <el-input v-model="formData.dataSource" placeholder="请输入数据来源" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任部门" prop="responsibleDept">
              <el-input v-model="formData.responsibleDept" placeholder="请输入责任部门" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 详细描述组 -->
      <div class="form-section">
        <h4 class="section-title">详细描述</h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="采集内容" prop="collectionContent">
              <Editor v-model="formData.collectionContent" height="120px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="案例示意" prop="caseDemo">
              <Editor v-model="formData.caseDemo" height="120px" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'

/** 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 表单 */
defineOptions({ name: 'CitystandardItemForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  standardId: undefined, // 标准体系ID
  seqNo: undefined,
  isCore: undefined,
  isNational: undefined,
  isLocal: undefined,
  isApplet: undefined,
  dimensionLevel1: undefined,
  dimensionLevel2: undefined,
  itemName: undefined,
  itemAlias: undefined,
  itemCode: undefined,
  explanation: undefined,
  evaluationCriteria: undefined,
  dataDecomposition: undefined,
  dataFormat: undefined,
  collectionContent: undefined,
  dataSource: undefined,
  responsibleDept: undefined,
  caseDemo: undefined
})
const formRules = reactive({
  seqNo: [{ required: true, message: '序号不能为空', trigger: 'blur' }],
  isCore: [{ required: true, message: '是否核心指标不能为空', trigger: 'blur' }],
  isNational: [{ required: true, message: '是否国家基础指标不能为空', trigger: 'blur' }],
  isLocal: [{ required: true, message: '是否地方特色指标不能为空', trigger: 'blur' }],
  isApplet: [{ required: true, message: '是否小程序采集不能为空', trigger: 'blur' }],
  dimensionLevel1: [{ required: true, message: '一级维度不能为空', trigger: 'blur' }],
  itemName: [{ required: true, message: '指标项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, standardId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 设置标准体系ID
  if (standardId) {
    formData.value.standardId = standardId
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CitystandardItemApi.getCitystandardItem(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CitystandardItemVO
    if (formType.value === 'create') {
      await CitystandardItemApi.createCitystandardItem(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardItemApi.updateCitystandardItem(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    standardId: undefined,
    seqNo: undefined,
    isCore: undefined,
    isNational: undefined,
    isLocal: undefined,
    isApplet: undefined,
    dimensionLevel1: undefined,
    dimensionLevel2: undefined,
    itemName: undefined,
    itemAlias: undefined,
    itemCode: undefined,
    explanation: undefined,
    evaluationCriteria: undefined,
    dataDecomposition: undefined,
    dataFormat: undefined,
    collectionContent: undefined,
    dataSource: undefined,
    responsibleDept: undefined,
    caseDemo: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 24px;

  .section-title {
    margin: 0 0 16px 0;
    padding: 8px 12px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    border-radius: 4px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}

// 富文本编辑器样式优化
:deep(.editor-container) {
  border-radius: 4px;

  .ql-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .ql-container {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .form-section {
    .el-row .el-col {
      margin-bottom: 12px;
    }
  }
}
</style>