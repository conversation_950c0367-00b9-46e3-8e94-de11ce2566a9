<template>
  <div class="citystandard-item-container">
    <!-- 左侧面板：标准体系选择器 -->
    <div class="left-panel">
      <StandardSelector
        ref="standardSelectorRef"
        :show-search="true"
        :show-pagination="true"
        :show-status="false"
        :table-height="'calc(100vh - 240px)'"
        :page-size="20"
        :enabled-only="true"
        @select="handleStandardSelect"
        @change="handleStandardChange"
      />
    </div>

    <!-- 右侧面板：指标目标管理 -->
    <div class="right-panel">
      <ContentWrap>
        <!-- 简化的主要查询条件 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="序号" prop="seqNo">
            <el-input
              v-model="queryParams.seqNo"
              placeholder="请输入序号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="核心指标" prop="isCore">
            <el-select
              v-model="queryParams.isCore"
              placeholder="请选择是否核心指标"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="指标项" prop="itemName">
            <el-input
              v-model="queryParams.itemName"
              placeholder="请输入指标项"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px" /> 搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px" /> 重置
            </el-button>
            <el-button @click="showAdvancedSearch = true">
              <Icon icon="ep:operation" class="mr-5px" /> 高级查询
            </el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['urban:citystandard-item:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['urban:citystandard-item:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- 数据表格区域 -->
      <ContentWrap>
        <!-- 空状态提示 -->
        <div v-if="!selectedStandard" class="empty-state">
          <Icon icon="ep:document" size="48" color="#c0c4cc" />
          <p>请从左侧选择一个城市指标体系</p>
          <p class="empty-desc">选择标准体系后，可以查看和管理对应的指标目标</p>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-else
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
          :height="'calc(100vh - 280px)'"
        >
          <el-table-column label="序号" align="center" prop="seqNo" width="80" />
          <el-table-column label="指标项" prop="itemName" min-width="150" show-overflow-tooltip />
          <el-table-column label="指标编号" prop="itemCode" width="120" show-overflow-tooltip />
          <el-table-column label="核心指标" align="center" prop="isCore" width="90">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isCore" />
            </template>
          </el-table-column>
          <el-table-column label="一级维度" prop="dimensionLevel1" width="120" show-overflow-tooltip />
          <el-table-column label="二级维度" prop="dimensionLevel2" width="120" show-overflow-tooltip />
          <el-table-column label="数据来源" prop="dataSource" width="120" show-overflow-tooltip />
          <el-table-column label="责任部门" prop="responsibleDept" width="120" show-overflow-tooltip />
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="160"
          />
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="openForm('update', scope.row.id)"
                v-hasPermi="['urban:citystandard-item:update']"
                size="small"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(scope.row.id)"
                v-hasPermi="['urban:citystandard-item:delete']"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <Pagination
          v-if="selectedStandard"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>

      <!-- 表单弹窗：添加/修改 -->
      <CitystandardItemForm ref="formRef" @success="getList" />
    </div>

    <!-- 高级查询抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级查询"
      :size="400"
      direction="rtl"
    >
      <el-form :model="queryParams" label-width="100px">
        <el-form-item label="是否国家基础指标" prop="isNational">
          <el-select
            v-model="queryParams.isNational"
            placeholder="请选择是否国家基础指标"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否地方特色指标" prop="isLocal">
          <el-select
            v-model="queryParams.isLocal"
            placeholder="请选择是否地方特色指标"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否小程序采集" prop="isApplet">
          <el-select
            v-model="queryParams.isApplet"
            placeholder="请选择是否小程序采集"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="一级维度" prop="dimensionLevel1">
          <el-input
            v-model="queryParams.dimensionLevel1"
            placeholder="请输入一级维度"
            clearable
          />
        </el-form-item>
        <el-form-item label="二级维度" prop="dimensionLevel2">
          <el-input
            v-model="queryParams.dimensionLevel2"
            placeholder="请输入二级维度"
            clearable
          />
        </el-form-item>
        <el-form-item label="指标项别名" prop="itemAlias">
          <el-input
            v-model="queryParams.itemAlias"
            placeholder="请输入指标项别名"
            clearable
          />
        </el-form-item>
        <el-form-item label="指标编号" prop="itemCode">
          <el-input
            v-model="queryParams.itemCode"
            placeholder="请输入指标编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="解释" prop="explanation">
          <el-input
            v-model="queryParams.explanation"
            placeholder="请输入解释"
            clearable
          />
        </el-form-item>
        <el-form-item label="评价标准" prop="evaluationCriteria">
          <el-input
            v-model="queryParams.evaluationCriteria"
            placeholder="请输入评价标准"
            clearable
          />
        </el-form-item>
        <el-form-item label="数据分解" prop="dataDecomposition">
          <el-input
            v-model="queryParams.dataDecomposition"
            placeholder="请输入数据分解"
            clearable
          />
        </el-form-item>
        <el-form-item label="数据格式" prop="dataFormat">
          <el-input
            v-model="queryParams.dataFormat"
            placeholder="请输入数据格式"
            clearable
          />
        </el-form-item>
        <el-form-item label="采集内容" prop="collectionContent">
          <el-input
            v-model="queryParams.collectionContent"
            placeholder="请输入采集内容"
            clearable
          />
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-input
            v-model="queryParams.dataSource"
            placeholder="请输入数据来源"
            clearable
          />
        </el-form-item>
        <el-form-item label="责任部门" prop="responsibleDept">
          <el-input
            v-model="queryParams.responsibleDept"
            placeholder="请输入责任部门"
            clearable
          />
        </el-form-item>
        <el-form-item label="案例示意" prop="caseDemo">
          <el-input
            v-model="queryParams.caseDemo"
            placeholder="请输入案例示意"
            clearable
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            style="width: 100%"
          />
        </el-form-item>

        <div class="drawer-footer">
          <el-button @click="handleAdvancedQuery">确认查询</el-button>
          <el-button @click="resetAdvancedQuery">清空条件</el-button>
          <el-button @click="showAdvancedSearch = false">取消</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import { CitystandardVO } from '@/api/urban/citystandard'
import CitystandardItemForm from './CitystandardItemForm.vue'
import { StandardSelector } from '@/components/StandardSelector'

/** 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 列表 */
defineOptions({ name: 'CitystandardItem' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CitystandardItemVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvancedSearch = ref(false) // 高级查询抽屉显示状态
const selectedStandard = ref<CitystandardVO | null>(null) // 当前选中的标准体系
const standardSelectorRef = ref() // 标准选择器引用

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  citystandardId: undefined, // 城市标准体系ID
  seqNo: undefined,
  isCore: undefined,
  isNational: undefined,
  isLocal: undefined,
  isApplet: undefined,
  dimensionLevel1: undefined,
  dimensionLevel2: undefined,
  itemName: undefined,
  itemAlias: undefined,
  itemCode: undefined,
  explanation: undefined,
  evaluationCriteria: undefined,
  dataDecomposition: undefined,
  dataFormat: undefined,
  collectionContent: undefined,
  dataSource: undefined,
  responsibleDept: undefined,
  caseDemo: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CitystandardItemApi.getCitystandardItemPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理标准体系选择 */
const handleStandardSelect = (standard: CitystandardVO) => {
  console.log('🎯 选择标准体系:', standard)
  selectedStandard.value = standard
  queryParams.citystandardId = standard.id
  queryParams.pageNo = 1
  getList()
}

/** 处理标准体系选择变化 */
const handleStandardChange = (standard: CitystandardVO | null) => {
  if (!standard) {
    selectedStandard.value = null
    queryParams.citystandardId = undefined
    list.value = []
    total.value = 0
  }
}

/** 高级查询确认 */
const handleAdvancedQuery = () => {
  queryParams.pageNo = 1
  getList()
  showAdvancedSearch.value = false
}

/** 重置高级查询条件 */
const resetAdvancedQuery = () => {
  // 重置高级查询条件，保留基础查询条件
  queryParams.isNational = undefined
  queryParams.isLocal = undefined
  queryParams.isApplet = undefined
  queryParams.dimensionLevel1 = undefined
  queryParams.dimensionLevel2 = undefined
  queryParams.itemAlias = undefined
  queryParams.itemCode = undefined
  queryParams.explanation = undefined
  queryParams.evaluationCriteria = undefined
  queryParams.dataDecomposition = undefined
  queryParams.dataFormat = undefined
  queryParams.collectionContent = undefined
  queryParams.dataSource = undefined
  queryParams.responsibleDept = undefined
  queryParams.caseDemo = undefined
  queryParams.createTime = []
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'create' && !selectedStandard.value) {
    message.warning('请先选择城市指标体系')
    return
  }
  const standardId = selectedStandard.value?.id
  formRef.value.open(type, id, standardId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CitystandardItemApi.deleteCitystandardItem(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CitystandardItemApi.exportCitystandardItem(queryParams)
    download.excel(data, '城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  // 不自动加载列表，等待用户选择标准体系
})
</script>

<style lang="scss" scoped>
.citystandard-item-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 16px;

  .left-panel {
    width: 300px;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;

    .panel-header {
      padding: 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #909399;

  p {
    margin: 16px 0 0 0;
    font-size: 16px;

    &.empty-desc {
      font-size: 14px;
      color: #c0c4cc;
      margin-top: 8px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 1200px) {
  .citystandard-item-container {
    flex-direction: column;
    height: auto;

    .left-panel {
      width: 100%;
      height: 400px;
    }

    .right-panel {
      height: auto;
    }
  }
}
</style>
