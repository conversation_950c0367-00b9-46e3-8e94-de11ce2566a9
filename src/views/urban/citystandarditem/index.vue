<template>
  <div class="citystandard-item-container">
    <!-- 左侧面板：标准体系选择器 -->
    <div class="left-panel">
      <div class="panel-header">
        <h3>城市指标体系</h3>
      </div>
      <StandardSelector
        ref="standardSelectorRef"
        :show-search="true"
        :show-pagination="true"
        :show-status="false"
        :table-height="'calc(100vh - 240px)'"
        :page-size="20"
        :enabled-only="true"
        @select="handleStandardSelect"
        @change="handleStandardChange"
      />
    </div>

    <!-- 右侧面板：指标目标管理 -->
    <div class="right-panel">
      <ContentWrap>
        <!-- 简化的主要查询条件 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="序号" prop="seqNo">
            <el-input
              v-model="queryParams.seqNo"
              placeholder="请输入序号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="是否核心指标" prop="isCore">
            <el-select
              v-model="queryParams.isCore"
              placeholder="请选择是否核心指标"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="指标项" prop="itemName">
            <el-input
              v-model="queryParams.itemName"
              placeholder="请输入指标项"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
            v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否地方特色指标" prop="isLocal">
        <el-select
          v-model="queryParams.isLocal"
          placeholder="请选择是否地方特色指标"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否小程序采集" prop="isApplet">
        <el-select
          v-model="queryParams.isApplet"
          placeholder="请选择是否小程序采集"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="一级维度" prop="dimensionLevel1">
        <el-input
          v-model="queryParams.dimensionLevel1"
          placeholder="请输入一级维度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="二级维度" prop="dimensionLevel2">
        <el-input
          v-model="queryParams.dimensionLevel2"
          placeholder="请输入二级维度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="指标项" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入指标项"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="指标项（别名）" prop="itemAlias">
        <el-input
          v-model="queryParams.itemAlias"
          placeholder="请输入指标项（别名）"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="指标编号" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入指标编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解释" prop="explanation">
        <el-input
          v-model="queryParams.explanation"
          placeholder="请输入解释"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评价标准" prop="evaluationCriteria">
        <el-input
          v-model="queryParams.evaluationCriteria"
          placeholder="请输入评价标准"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据分解" prop="dataDecomposition">
        <el-input
          v-model="queryParams.dataDecomposition"
          placeholder="请输入数据分解"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据格式" prop="dataFormat">
        <el-input
          v-model="queryParams.dataFormat"
          placeholder="请输入数据格式"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-input
          v-model="queryParams.dataSource"
          placeholder="请输入数据来源"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="责任部门" prop="responsibleDept">
        <el-input
          v-model="queryParams.responsibleDept"
          placeholder="请输入责任部门"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['urban:citystandard-item:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['urban:citystandard-item:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="序号" align="center" prop="seqNo" />
      <el-table-column label="是否核心指标" align="center" prop="isCore">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isCore" />
        </template>
      </el-table-column>
      <el-table-column label="是否国家基础指标" align="center" prop="isNational">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isNational" />
        </template>
      </el-table-column>
      <el-table-column label="是否地方特色指标" align="center" prop="isLocal">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isLocal" />
        </template>
      </el-table-column>
      <el-table-column label="是否小程序采集" align="center" prop="isApplet">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isApplet" />
        </template>
      </el-table-column>
      <el-table-column label="一级维度" align="center" prop="dimensionLevel1" />
      <el-table-column label="二级维度" align="center" prop="dimensionLevel2" />
      <el-table-column label="指标项" align="center" prop="itemName" />
      <el-table-column label="指标项（别名）" align="center" prop="itemAlias" />
      <el-table-column label="指标编号" align="center" prop="itemCode" />
      <el-table-column label="解释" align="center" prop="explanation" />
      <el-table-column label="评价标准" align="center" prop="evaluationCriteria" />
      <el-table-column label="数据分解" align="center" prop="dataDecomposition" />
      <el-table-column label="数据格式" align="center" prop="dataFormat" />
      <el-table-column label="采集内容" align="center" prop="collectionContent" />
      <el-table-column label="数据来源" align="center" prop="dataSource" />
      <el-table-column label="责任部门" align="center" prop="responsibleDept" />
      <el-table-column label="案例示意" align="center" prop="caseDemo" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['urban:citystandard-item:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['urban:citystandard-item:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CitystandardItemForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import CitystandardItemForm from './CitystandardItemForm.vue'

/** 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 列表 */
defineOptions({ name: 'CitystandardItem' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CitystandardItemVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  seqNo: undefined,
  isCore: undefined,
  isNational: undefined,
  isLocal: undefined,
  isApplet: undefined,
  dimensionLevel1: undefined,
  dimensionLevel2: undefined,
  itemName: undefined,
  itemAlias: undefined,
  itemCode: undefined,
  explanation: undefined,
  evaluationCriteria: undefined,
  dataDecomposition: undefined,
  dataFormat: undefined,
  collectionContent: undefined,
  dataSource: undefined,
  responsibleDept: undefined,
  caseDemo: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CitystandardItemApi.getCitystandardItemPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CitystandardItemApi.deleteCitystandardItem(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CitystandardItemApi.exportCitystandardItem(queryParams)
    download.excel(data, '城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>