<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="发布年份" prop="publishYear">
        <el-date-picker
          v-model="queryParams.publishYear"
          value-format="YYYY"
          type="year"
          placeholder="选择发布年份"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button @click="showAdvancedSearch = true"><Icon icon="ep:operation" class="mr-5px" /> 高级查询</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['urban:citystandard:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['urban:citystandard:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 高级查询抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级查询"
      :size="400"
      direction="rtl"
    >
      <el-form :model="queryParams" label-width="80px">
        <el-form-item label="分类" prop="category">
          <el-input
            v-model="queryParams.category"
            placeholder="请输入分类"
            clearable
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-select
            v-model="queryParams.enabled"
            placeholder="请选择是否启用"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_CITYSTANDARD_ENABLED)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            style="width: 100%"
          />
        </el-form-item>

        <div class="drawer-footer">
          <el-button @click="handleAdvancedQuery">确认查询</el-button>
          <el-button @click="resetAdvancedQuery">清空条件</el-button>
          <el-button @click="showAdvancedSearch = false">取消</el-button>
        </div>
      </el-form>
    </el-drawer>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="发布年份" align="center" prop="publishYear" />
      <el-table-column label="分类" align="center" prop="category">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_CITYSTANDARD_CATEGORY" :value="scope.row.category" />
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" prop="enabled">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_CITYSTANDARD_ENABLED" :value="scope.row.enabled" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['urban:citystandard:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['urban:citystandard:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CitystandardForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import CitystandardForm from './CitystandardForm.vue'

/** 城市标准体体系 列表 */
defineOptions({ name: 'Citystandard' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CitystandardVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvancedSearch = ref(false) // 高级查询抽屉显示状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  publishYear: undefined,
  category: undefined,
  enabled: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CitystandardApi.getCitystandardPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 高级查询确认 */
const handleAdvancedQuery = () => {
  queryParams.pageNo = 1
  showAdvancedSearch.value = false
  getList()
}

/** 重置高级查询条件 */
const resetAdvancedQuery = () => {
  // 重置高级查询相关字段
  queryParams.category = undefined
  queryParams.enabled = undefined
  queryParams.createTime = []
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CitystandardApi.deleteCitystandard(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CitystandardApi.exportCitystandard(queryParams)
    download.excel(data, '城市标准体体系.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.drawer-footer {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>