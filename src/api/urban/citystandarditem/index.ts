import request from '@/config/axios'

// 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 VO
export interface CitystandardItemVO {
  id: number // ID
  seqNo: number // 序号
  isCore: number // 是否核心指标
  isNational: number // 是否国家基础指标
  isLocal: number // 是否地方特色指标
  isApplet: number // 是否小程序采集
  dimensionLevel1: string // 一级维度
  dimensionLevel2: string // 二级维度
  itemName: string // 指标项
  itemAlias: string // 指标项（别名）
  itemCode: string // 指标编号
  explanation: string // 解释
  evaluationCriteria: string // 评价标准
  dataDecomposition: string // 数据分解
  dataFormat: string // 数据格式
  collectionContent: string // 采集内容
  dataSource: string // 数据来源
  responsibleDept: string // 责任部门
  caseDemo: string // 案例示意
}

// 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 API
export const CitystandardItemApi = {
  // 查询城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性分页
  getCitystandardItemPage: async (params: any) => {
    return await request.get({ url: `/urban/citystandard-item/page`, params })
  },

  // 查询城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性详情
  getCitystandardItem: async (id: number) => {
    return await request.get({ url: `/urban/citystandard-item/get?id=` + id })
  },

  // 新增城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性
  createCitystandardItem: async (data: CitystandardItemVO) => {
    return await request.post({ url: `/urban/citystandard-item/create`, data })
  },

  // 修改城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性
  updateCitystandardItem: async (data: CitystandardItemVO) => {
    return await request.put({ url: `/urban/citystandard-item/update`, data })
  },

  // 删除城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性
  deleteCitystandardItem: async (id: number) => {
    return await request.delete({ url: `/urban/citystandard-item/delete?id=` + id })
  },

  // 导出城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 Excel
  exportCitystandardItem: async (params) => {
    return await request.download({ url: `/urban/citystandard-item/export-excel`, params })
  }
}