# Context
Filename: 城市指标目标管理页面改造任务.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
调整城市指标目标管理页面（src/views/urban/citystandarditem/index.vue）和表单页面（CitystandardItemForm.vue），实现以下功能：

1. 整个index.vue首页需要以左右布局，左边结合StandardSelector组件将城市指标体系选择放置，右侧为当前指标目标列表
2. 指标目标列表的查询条件需要简化，保留主要内容，其余的以"高级查询"按钮进行隐藏，点击高级查询右侧抽拉式展示所有额外的查询条件
3. 指标目标表单页面中，需要根据实现合理的布局样式注意数据的类型与校验，布局美观，尽量不要有滚动

# Project Overview
这是一个基于Vue3 + Element Plus的城市健康检查前端项目，使用TypeScript开发。项目中已有StandardSelector组件用于标准体系选择，以及高级查询抽屉的实现模式。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
## 当前页面结构分析
- index.vue：传统的单列布局，包含搜索表单和数据表格
- 搜索表单包含多个查询条件：序号、是否核心指标、是否国家基础指标、是否地方特色指标、是否小程序采集、一级维度、二级维度、指标项、指标项别名、指标编号、解释、评价标准、数据分解、数据格式、采集内容、数据来源、责任部门、案例示意、创建时间
- 表格显示所有字段信息

## StandardSelector组件分析
- 位置：src/components/StandardSelector/src/StandardSelector.vue
- 功能：城市标准体系选择器，支持搜索、分页、选择事件
- 事件：select（选择标准时触发）、change（选择变化时触发）
- 方法：getSelectedStandard、clearSelection、refresh、selectById
- 样式：已有完整的左右布局示例和样式

## 表单页面分析
- CitystandardItemForm.vue：使用Dialog弹窗形式
- 包含大量表单字段，当前为单列布局
- 使用Editor组件处理富文本内容（采集内容、案例示意）
- 需要优化布局避免滚动

## 高级查询模式分析
- 项目中已有高级查询抽屉实现模式（参考citystandard/index.vue和task/index.vue）
- 使用el-drawer组件，direction="rtl"（从右侧滑出）
- 抽屉内包含额外的查询条件和操作按钮

## 关键约束和要求
- 需要与城市指标体系关联，通过StandardSelector选择
- 保持现有API接口不变
- 遵循项目现有的UI/UX模式
- 表单需要美观且避免滚动

# Proposed Solution (Populated by INNOVATE mode)
## 方案一：完整重构布局
**优点：**
- 用户体验最佳，符合现代化管理界面设计
- 左右布局清晰分离关注点
- 高级查询抽屉提供更好的空间利用

**缺点：**
- 改动较大，需要重新设计整个页面结构
- 需要处理响应式布局适配

**实现要点：**
- 使用flex布局实现左右分栏
- 左侧固定宽度（约400px）放置StandardSelector
- 右侧自适应宽度显示指标目标列表
- 简化主要查询条件，其余移至高级查询抽屉
- 表单采用多列布局优化显示

## 方案二：渐进式改造
**优点：**
- 风险较小，可以分步实施
- 保持现有功能稳定性

**缺点：**
- 可能存在布局不够协调的问题
- 用户体验提升有限

**最终选择：方案一**
考虑到用户明确要求左右布局和高级查询功能，选择完整重构能够提供最佳的用户体验和功能完整性。

# Implementation Plan (Generated by PLAN mode)

## 详细实现计划

### 1. 页面布局重构
**文件：** src/views/urban/citystandarditem/index.vue
**改动内容：**
- 重构模板结构为左右布局
- 左侧集成StandardSelector组件
- 右侧重新设计查询区域和表格区域
- 添加高级查询抽屉

### 2. 查询条件优化
**保留主要查询条件：**
- 序号
- 是否核心指标
- 指标项名称

**移至高级查询：**
- 是否国家基础指标
- 是否地方特色指标  
- 是否小程序采集
- 一级维度
- 二级维度
- 指标项别名
- 指标编号
- 解释
- 评价标准
- 数据分解
- 数据格式
- 采集内容
- 数据来源
- 责任部门
- 案例示意
- 创建时间

### 3. 表单布局优化
**文件：** src/views/urban/citystandarditem/CitystandardItemForm.vue
**改动内容：**
- 采用多列布局（2-3列）
- 合理分组相关字段
- 优化富文本编辑器高度
- 确保无滚动条显示

### 4. 数据关联处理
- 添加标准体系ID字段关联
- 修改API调用逻辑支持按标准体系筛选
- 处理标准体系选择变化事件

## Implementation Checklist:
1. 重构index.vue页面布局为左右分栏结构
2. 集成StandardSelector组件到左侧面板
3. 简化右侧主要查询条件区域
4. 实现高级查询抽屉功能
5. 优化表格显示和分页组件布局
6. 重构CitystandardItemForm.vue为多列布局
7. 优化表单字段分组和样式
8. 添加标准体系关联逻辑
9. 处理响应式布局适配
10. 测试所有功能完整性

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> 待开始执行

# Task Progress (Appended by EXECUTE mode after each step completion)
*待执行步骤后更新*

# Final Review (Populated by REVIEW mode)
*待REVIEW模式时填充*
